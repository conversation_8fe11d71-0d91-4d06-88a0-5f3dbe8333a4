/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as crons from "../crons.js";
import type * as erstellung_index from "../erstellung/index.js";
import type * as erstellung_leistung from "../erstellung/leistung.js";
import type * as erstellung_lieferschein from "../erstellung/lieferschein.js";
import type * as erstellung_lieferscheineConfig from "../erstellung/lieferscheineConfig.js";
import type * as erstellung_uebersichtenConfig from "../erstellung/uebersichtenConfig.js";
import type * as kunden_dokumentation from "../kunden/dokumentation.js";
import type * as kunden_index from "../kunden/index.js";
import type * as kunden_kontingente from "../kunden/kontingente.js";
import type * as kunden_stammdaten from "../kunden/stammdaten.js";
import type * as system_dokuKategorien from "../system/dokuKategorien.js";
import type * as system_dokuKategorienConfig from "../system/dokuKategorienConfig.js";
import type * as system_email from "../system/email.js";
import type * as system_emailConfig from "../system/emailConfig.js";
import type * as system_feedback from "../system/feedback.js";
import type * as system_index from "../system/index.js";
import type * as system_standards from "../system/standards.js";
import type * as verwaltung_index from "../verwaltung/index.js";
import type * as verwaltung_mitarbeiter from "../verwaltung/mitarbeiter.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  crons: typeof crons;
  "erstellung/index": typeof erstellung_index;
  "erstellung/leistung": typeof erstellung_leistung;
  "erstellung/lieferschein": typeof erstellung_lieferschein;
  "erstellung/lieferscheineConfig": typeof erstellung_lieferscheineConfig;
  "erstellung/uebersichtenConfig": typeof erstellung_uebersichtenConfig;
  "kunden/dokumentation": typeof kunden_dokumentation;
  "kunden/index": typeof kunden_index;
  "kunden/kontingente": typeof kunden_kontingente;
  "kunden/stammdaten": typeof kunden_stammdaten;
  "system/dokuKategorien": typeof system_dokuKategorien;
  "system/dokuKategorienConfig": typeof system_dokuKategorienConfig;
  "system/email": typeof system_email;
  "system/emailConfig": typeof system_emailConfig;
  "system/feedback": typeof system_feedback;
  "system/index": typeof system_index;
  "system/standards": typeof system_standards;
  "verwaltung/index": typeof verwaltung_index;
  "verwaltung/mitarbeiter": typeof verwaltung_mitarbeiter;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
