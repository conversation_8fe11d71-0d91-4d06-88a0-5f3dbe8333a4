/**
 * Configuration file for Übersichten
 * This file contains all settings for Übersicht documents, email templates and behavior
 */

// Übersicht specific settings
export interface UebersichtSettings {
	// Display options
	includeHeader: boolean;
	showLogo: boolean;
	includeFooter: boolean;
	includeLeistungsuebersicht: boolean;
	includeKontingentuebersicht: boolean;
	includeSummary: boolean;

	// Content
	fusszeileText: string;
	logoPath: string;
}

// Email specific settings for Übersichten
export interface UebersichtEmailSettings {
	// Display options
	defaultSendToMitarbeiter: boolean;
	defaultSendToKunde: boolean;
}

// Email template for Übersichten
export interface UebersichtEmailTemplate {
	subject: string;
	body: string;
}

// TurboSMTP configuration (shared)
export interface EmailConfig {
	consumerKey: string;
	consumerSecret: string;
	sender: {
		email: string;
		name: string;
	};
	endpoint: string;
}

// Complete Übersicht configuration
export interface UebersichtConfig {
	settings: UebersichtSettings;
	emailSettings: UebersichtEmailSettings;
	emailTemplate: UebersichtEmailTemplate;
}

// Default Übersicht settings
export const defaultUebersichtSettings: UebersichtSettings = {
	// Display options
	includeHeader: true,
	showLogo: true,
	includeFooter: true,
	includeLeistungsuebersicht: true,
	includeKontingentuebersicht: true,
	includeSummary: true,

	// Content
	fusszeileText: "© innov8-IT",
	logoPath: "/logos/logo.png",
};

// Default email settings for Übersichten
export const defaultUebersichtEmailSettings: UebersichtEmailSettings = {
	// Display options
	defaultSendToMitarbeiter: true,
	defaultSendToKunde: false,
};

// Default email template for Übersichten
export const defaultUebersichtEmailTemplate: UebersichtEmailTemplate = {
	subject: "Übersicht: {{kunde}} - {{zeitraum}}",
	body: `Sehr geehrte(r) {{empfaenger}},

anbei erhalten Sie die Übersicht für {{kunde}} für den Zeitraum {{zeitraum}}.

Mit freundlichen Grüßen,
Ihre innov8-IT`,
};

// Default email configuration (shared)
export const defaultEmailConfig: EmailConfig = {
	consumerKey: "",
	consumerSecret: "",
	sender: {
		email: "",
		name: "",
	},
	endpoint: "",
};

// Complete default Übersicht configuration
export const defaultUebersichtConfig: UebersichtConfig = {
	settings: defaultUebersichtSettings,
	emailSettings: defaultUebersichtEmailSettings,
	emailTemplate: defaultUebersichtEmailTemplate,
};

// Function to get Übersicht settings
export function getUebersichtSettings(): UebersichtSettings {
	return defaultUebersichtSettings;
}

// Function to get Übersicht email settings
export function getUebersichtEmailSettings(): UebersichtEmailSettings {
	return defaultUebersichtEmailSettings;
}

// Function to get Übersicht email template
export function getUebersichtEmailTemplate(): UebersichtEmailTemplate {
	return defaultUebersichtEmailTemplate;
}

// Function to get email configuration with environment variables
export function getEmailConfig(): EmailConfig {
	return {
		consumerKey: process.env.TURBOSMTP_CONSUMER_KEY || "",
		consumerSecret: process.env.TURBOSMTP_CONSUMER_SECRET || "",
		sender: {
			email: process.env.EMAIL_FROM || "",
			name: process.env.EMAIL_FROM_NAME || "",
		},
		endpoint: process.env.TURBOSMTP_API_URL || "",
	};
}

// Function to get complete Übersicht configuration
export function getUebersichtConfig(): UebersichtConfig {
	return defaultUebersichtConfig;
}
