/**
 * Configuration file for Lieferscheine
 * This file contains all settings for Lieferschein documents, email templates and behavior
 */

// Lieferschein specific settings
export interface LieferscheinSettings {
	// Display options
	includeHeader: boolean;
	showLogo: boolean;
	includeFooter: boolean;
	includeSignatureField: boolean;

	// Content
	fusszeileText: string;
	logoPath: string;
	legalText: string;
	signatureText: string;
}

// Email specific settings for Lieferscheine
export interface LieferscheinEmailSettings {
	// Display options
	defaultSendToMitarbeiter: boolean;
	defaultSendToKunde: boolean;
}

// Email template for Lieferscheine
export interface LieferscheinEmailTemplate {
	subject: string;
	body: string;
}

// TurboSMTP configuration (shared)
export interface EmailConfig {
	consumerKey: string;
	consumerSecret: string;
	sender: {
		email: string;
		name: string;
	};
	endpoint: string;
}

// Complete Lieferschein configuration
export interface LieferscheinConfig {
	settings: LieferscheinSettings;
	emailSettings: LieferscheinEmailSettings;
	emailTemplate: LieferscheinEmailTemplate;
}

// Default Lieferschein settings
export const defaultLieferscheinSettings: LieferscheinSettings = {
	// Display options
	includeHeader: true,
	showLogo: true,
	includeFooter: true,
	includeSignatureField: true,

	// Content
	legalText:
		"Grundlage der Geschäftsbeziehung bilden die Allgemeinen Geschäftsbedingungen (AGB), der IT-Dienstleistungsvertrag (Rahmenvertrag), die Auftragsverarbeitungsvereinbarung (AVV) sowie die Datenschutzerklärung der innov8-IT, jeweils in ihrer aktuell gültigen Fassung.\n\nAlle Preise sind Nettopreise und verstehen sich zuzüglich der gesetzlichen Mehrwertsteuer in Höhe von derzeit 19%.\n\nLeistungen und Waren gelten als abgenommen und genehmigt, sofern innerhalb von 7 Tagen nach Erhalt des Lieferscheins weder eine Unterschrift des Auftraggebers erfolgt noch ein schriftlicher Widerspruch oder eine Mängelrüge eingereicht wird.",
	signatureText: "Ort / Datum / Unterschrift / Stempel",
	fusszeileText: "© innov8-IT",
	logoPath: "/logos/logo.png",
};

// Default email settings for Lieferscheine
export const defaultLieferscheinEmailSettings: LieferscheinEmailSettings = {
	// Display options
	defaultSendToMitarbeiter: true,
	defaultSendToKunde: false,
};

// Default email template for Lieferscheine
export const defaultLieferscheinEmailTemplate: LieferscheinEmailTemplate = {
	subject: "Lieferschein: {{nummer}}",
	body: `Sehr geehrte(r) {{empfaenger}},

anbei erhalten Sie den Lieferschein {{nummer}} vom {{datum}}.

Mit freundlichen Grüßen,
Ihre innov8-IT`,
};

// Default email configuration (shared)
export const defaultEmailConfig: EmailConfig = {
	consumerKey: "",
	consumerSecret: "",
	sender: {
		email: "",
		name: "",
	},
	endpoint: "",
};

// Complete default Lieferschein configuration
export const defaultLieferscheinConfig: LieferscheinConfig = {
	settings: defaultLieferscheinSettings,
	emailSettings: defaultLieferscheinEmailSettings,
	emailTemplate: defaultLieferscheinEmailTemplate,
};

// Function to get Lieferschein settings
export function getLieferscheinSettings(): LieferscheinSettings {
	return defaultLieferscheinSettings;
}

// Function to get Lieferschein email settings
export function getLieferscheinEmailSettings(): LieferscheinEmailSettings {
	return defaultLieferscheinEmailSettings;
}

// Function to get Lieferschein email template
export function getLieferscheinEmailTemplate(): LieferscheinEmailTemplate {
	return defaultLieferscheinEmailTemplate;
}

// Function to get email configuration with environment variables
export function getEmailConfig(): EmailConfig {
	return {
		consumerKey: process.env.TURBOSMTP_CONSUMER_KEY || "",
		consumerSecret: process.env.TURBOSMTP_CONSUMER_SECRET || "",
		sender: {
			email: process.env.EMAIL_FROM || "",
			name: process.env.EMAIL_FROM_NAME || "",
		},
		endpoint: process.env.TURBOSMTP_API_URL || "",
	};
}

// Function to get complete Lieferschein configuration
export function getLieferscheinConfig(): LieferscheinConfig {
	return defaultLieferscheinConfig;
}
